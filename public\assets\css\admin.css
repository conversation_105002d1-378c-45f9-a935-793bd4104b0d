/* Admin Panel Styles */

.admin-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.admin-header {
    text-align: center;
    margin-bottom: 50px;
    color: white;
}

.admin-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.admin-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.access-denied {
    background: white;
    border-radius: 15px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    max-width: 500px;
    margin: 0 auto;
}

.access-denied-content i {
    font-size: 4rem;
    color: #e74c3c;
    margin-bottom: 20px;
}

.access-denied-content h2 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.access-denied-content p {
    color: #7f8c8d;
    margin-bottom: 30px;
}

.admin-content {
    display: grid;
    gap: 30px;
}

.admin-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.card-header i {
    margin-right: 10px;
}

.btn-refresh {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-refresh:hover {
    background: rgba(255,255,255,0.3);
}

.card-content {
    padding: 30px;
}

.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #bdc3c7;
}

.users-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #ecf0f1;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.users-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #ecf0f1;
}

.users-table td {
    padding: 15px;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: middle;
}

.users-table tr:hover {
    background: #f8f9fa;
}

.btn-delete {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.loading-message, .no-data-message {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

.loading-message i {
    font-size: 2rem;
    margin-bottom: 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-data-message i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #bdc3c7;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.stat-icon {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 15px;
}

.stat-info h4 {
    font-size: 2rem;
    color: #2c3e50;
    margin: 0 0 5px 0;
    font-weight: 700;
}

.stat-info p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
}

.admin-loading {
    text-align: center;
    padding: 100px 20px;
    color: white;
}

.admin-loading i {
    font-size: 3rem;
    margin-bottom: 20px;
    animation: spin 1s linear infinite;
}

.admin-loading p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 20px 30px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close {
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 30px;
}

.modal-body p {
    margin-bottom: 15px;
    color: #2c3e50;
}

.modal-body .warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px 15px;
    border-radius: 5px;
    font-weight: 600;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #ecf0f1;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #95a5a6;
    color: white;
}

.btn-cancel:hover {
    background: #7f8c8d;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

/* Message Styles */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1001;
    animation: slideInRight 0.3s ease;
    max-width: 400px;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.success-message {
    background: linear-gradient(135deg, #27ae60, #229954);
    border-left: 4px solid #1e8449;
}

.error-message {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-left: 4px solid #a93226;
}

/* Admin Navigation */
.admin-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.nav-btn i {
    font-size: 1.1rem;
}

/* Admin Sections */
.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

/* Class Grid */
.class-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.class-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.class-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.class-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.class-icon {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 20px;
}

.class-card h4 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.class-card p {
    color: #7f8c8d;
    margin-bottom: 25px;
    font-size: 0.95rem;
}

.class-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(149, 165, 166, 0.4);
}

/* Stat Cards */
.stat-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header h1 {
        font-size: 2rem;
    }

    .admin-nav {
        flex-direction: column;
        gap: 5px;
    }

    .nav-btn {
        justify-content: center;
    }

    .card-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .class-grid {
        grid-template-columns: 1fr;
    }

    .class-actions {
        flex-direction: column;
    }

    .users-table {
        font-size: 0.9rem;
    }

    .users-table th,
    .users-table td {
        padding: 10px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .message {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
