/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, rgba(255,255,255,0.97) 0%, rgba(247,237,255,0.97) 50%, rgba(235,244,255,0.97) 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header & Navigation */
header {
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 15px rgba(66, 133, 244, 0.15);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    border-bottom: 2px solid rgba(255, 122, 168, 0.3);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.logo img {
    height: 50px;
    border-radius: 5px;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 25px;
}

nav ul li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 5px 10px;
    transition: all 0.3s ease;
    position: relative;
}

nav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: linear-gradient(to right, #4285F4, #ff7aa8);
    transition: width 0.3s ease;
}

nav ul li a:hover::after, nav ul li a.active::after {
    width: 100%;
}

nav ul li a:hover, nav ul li a.active {
    color: #4285F4;
}

/* Hero Section */
.hero {
    height: 100vh;
    background-image: url('../images/background.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(66, 133, 244, 0.7) 0%, rgba(255, 122, 168, 0.7) 100%);
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    padding: 20px;
    animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero p {
    font-size: 1.5rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Buttons */
.btn {
    display: inline-block;
    background: linear-gradient(to right, #4285F4, #ff7aa8);
    color: white;
    padding: 12px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(66, 133, 244, 0.3);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
    transition: left 0.7s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(66, 133, 244, 0.4);
}

/* Footer */
footer {
    background-color: white;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid #e9ecef;
    position: relative;
    margin-top: auto;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #4285F4, #ff7aa8);
}

/* Main content area */
main, section {
    flex: 1;
}

/* Common Elements */
.page-header {
    text-align: center;
    margin: 120px 0 50px;
    animation: fadeIn 0.8s ease-out forwards;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.page-header h1::after {
    content: '';
    position: absolute;
    width: 50%;
    height: 3px;
    bottom: -10px;
    left: 25%;
    background: linear-gradient(to right, #4285F4, #ff7aa8);
    border-radius: 2px;
}

.page-header p {
    color: #666;
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Form validation styles */
.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #ff4d4d;
    background-color: rgba(255, 77, 77, 0.05);
}

.success-message {
    background-color: rgba(76, 217, 100, 0.1);
    color: #2ca745;
    padding: 15px;
    margin-top: 20px;
    border-radius: 5px;
    font-weight: 500;
    border: 1px solid #2ca745;
    animation: fadeIn 0.5s ease-out forwards;
}

.error-message {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    padding: 15px;
    margin-top: 20px;
    border-radius: 5px;
    font-weight: 500;
    border: 1px solid #e74c3c;
    animation: fadeIn 0.5s ease-out forwards;
}

/* Animation for elements */
.animated {
    animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Mobile navigation */
.menu-toggle {
    display: none;
    cursor: pointer;
    font-size: 1.5rem;
    color: #333;
}

@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }
    
    header .container {
        flex-direction: column;
    }
    
    nav {
        width: 100%;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease;
    }
    
    nav.active {
        max-height: 300px;
    }
    
    nav ul {
        flex-direction: column;
        margin-top: 15px;
    }
    
    nav ul li {
        margin: 5px 0;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .hero p {
        font-size: 1.2rem;
    }
}

/* Animation Effects */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(to right, #4285F4, #ff7aa8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

/* Contact Information */
.contact-info {
    margin-top: 50px;
    animation: fadeIn 1.2s ease-out forwards;
}

.contact-info h3 {
    font-size: 2rem;
    margin-bottom: 30px;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.contact-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.contact-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.contact-item i {
    font-size: 2.5rem;
    color: #fff;
    min-width: 50px;
}

.contact-details h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.contact-details p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
}

.facebook-link {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
}

.facebook-link:hover {
    color: white;
    text-decoration: none;
}