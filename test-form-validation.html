<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Validation Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .admin-section {
            border-color: #e74c3c;
            background-color: #fdf2f2;
        }
        .student-section {
            border-color: #4285F4;
            background-color: #f2f6ff;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        input:disabled, select:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
        .btn {
            background: linear-gradient(45deg, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: bold;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: rgba(76, 217, 100, 0.2);
            color: #2ca745;
            border: 1px solid #2ca745;
        }
        .error {
            background-color: rgba(255, 77, 77, 0.2);
            color: #ff4d4d;
            border: 1px solid #ff4d4d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Form Validation Fix Test</h1>
        <p>Test để kiểm tra lỗi "An invalid form control with name='' is not focusable" đã được sửa chưa.</p>
        
        <div id="status" class="status">
            <strong>Trạng thái:</strong> Sẵn sàng test
        </div>

        <form id="testForm">
            <!-- Admin Fields -->
            <div id="adminSection" class="form-section admin-section">
                <h3>🔴 Admin Fields (Ẩn cho student)</h3>
                <div class="form-group">
                    <label for="adminFullName">Họ và tên (Admin)</label>
                    <input type="text" id="adminFullName" required>
                </div>
                <div class="form-group">
                    <label for="adminGender">Giới tính (Admin)</label>
                    <select id="adminGender" required>
                        <option value="">-- Chọn --</option>
                        <option value="male">Nam</option>
                        <option value="female">Nữ</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="adminBirthdate">Ngày sinh (Admin)</label>
                    <input type="date" id="adminBirthdate" required>
                </div>
            </div>

            <!-- Student Fields -->
            <div id="studentSection" class="form-section student-section">
                <h3>🔵 Student Fields (Hiển thị cho student)</h3>
                <div class="form-group">
                    <label for="studentFullName">Họ và tên (Student)</label>
                    <input type="text" id="studentFullName" required>
                </div>
                <div class="form-group">
                    <label for="studentGender">Giới tính (Student)</label>
                    <select id="studentGender" required>
                        <option value="">-- Chọn --</option>
                        <option value="male">Nam</option>
                        <option value="female">Nữ</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="studentBirthdate">Ngày sinh (Student)</label>
                    <input type="date" id="studentBirthdate" required>
                </div>
                <div class="form-group">
                    <label for="studentId">Mã học sinh</label>
                    <input type="text" id="studentId" required>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="button" class="btn" onclick="simulateStudentMode()">Chế độ Student</button>
                <button type="button" class="btn" onclick="simulateAdminMode()">Chế độ Admin</button>
                <button type="submit" class="btn">Submit Form</button>
            </div>
        </form>

        <div style="margin-top: 30px;">
            <h3>📋 Hướng dẫn test:</h3>
            <ol>
                <li>Click "Chế độ Student" - Admin fields sẽ bị ẩn và disabled</li>
                <li>Click "Submit Form" - Không được có lỗi console</li>
                <li>Click "Chế độ Admin" - Student fields sẽ bị ẩn và disabled</li>
                <li>Click "Submit Form" - Không được có lỗi console</li>
                <li>Mở Console (F12) để kiểm tra lỗi</li>
            </ol>
        </div>
    </div>

    <script>
        function simulateStudentMode() {
            const adminSection = document.getElementById('adminSection');
            const studentSection = document.getElementById('studentSection');
            const status = document.getElementById('status');
            
            // Hide admin section, show student section
            adminSection.style.display = 'none';
            studentSection.style.display = 'block';
            
            // Disable admin fields, enable student fields
            const adminInputs = adminSection.querySelectorAll('input, select');
            const studentInputs = studentSection.querySelectorAll('input, select');
            
            adminInputs.forEach(input => {
                input.disabled = true;
                input.required = false;
            });
            
            studentInputs.forEach(input => {
                input.disabled = false;
                input.required = true;
            });
            
            status.innerHTML = '<strong>Chế độ:</strong> Student (Admin fields ẩn và disabled)';
            status.className = 'status success';
        }
        
        function simulateAdminMode() {
            const adminSection = document.getElementById('adminSection');
            const studentSection = document.getElementById('studentSection');
            const status = document.getElementById('status');
            
            // Show admin section, hide student section
            adminSection.style.display = 'block';
            studentSection.style.display = 'none';
            
            // Enable admin fields, disable student fields
            const adminInputs = adminSection.querySelectorAll('input, select');
            const studentInputs = studentSection.querySelectorAll('input, select');
            
            adminInputs.forEach(input => {
                input.disabled = false;
                input.required = true;
            });
            
            studentInputs.forEach(input => {
                input.disabled = true;
                input.required = false;
            });
            
            status.innerHTML = '<strong>Chế độ:</strong> Admin (Student fields ẩn và disabled)';
            status.className = 'status success';
        }
        
        // Form submit handler
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const status = document.getElementById('status');
            
            try {
                // This should not cause any console errors now
                console.log('Form submitted successfully without validation errors');
                status.innerHTML = '<strong>✅ Thành công:</strong> Form submit không có lỗi console!';
                status.className = 'status success';
            } catch (error) {
                console.error('Form submission error:', error);
                status.innerHTML = '<strong>❌ Lỗi:</strong> ' + error.message;
                status.className = 'status error';
            }
        });
        
        // Initialize in student mode
        simulateStudentMode();
    </script>
</body>
</html>
