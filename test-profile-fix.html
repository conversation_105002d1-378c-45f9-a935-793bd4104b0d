<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Fix - Vthon</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: linear-gradient(45deg, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: rgba(76, 217, 100, 0.2);
            color: #2ca745;
            border: 1px solid #2ca745;
        }
        .error {
            background-color: rgba(255, 77, 77, 0.2);
            color: #ff4d4d;
            border: 1px solid #ff4d4d;
        }
        .info {
            background-color: rgba(66, 133, 244, 0.2);
            color: #4285F4;
            border: 1px solid #4285F4;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Profile Fix Test Page</h1>
        <p>This page helps test and verify the profile saving functionality fixes.</p>
        
        <div class="status info">
            <strong>Test Status:</strong> Ready to test
        </div>

        <h2>🧪 Test Functions</h2>
        <button class="test-button" onclick="testFirebaseConnection()">Test Firebase Connection</button>
        <button class="test-button" onclick="testAuthState()">Test Auth State</button>
        <button class="test-button" onclick="testFormFields()">Test Form Fields</button>
        <button class="test-button" onclick="testFirebaseWrite()">Test Firebase Write</button>
        <button class="test-button" onclick="openAuthPage()">Open Auth Page</button>

        <h2>📋 Test Results</h2>
        <div id="testResults"></div>

        <h2>🔍 Debug Information</h2>
        <pre id="debugInfo">Click test buttons to see debug information...</pre>
    </div>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, setDoc, getDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Global test functions
        window.testFirebaseConnection = function() {
            const results = document.getElementById('testResults');
            const debug = document.getElementById('debugInfo');
            
            try {
                const info = {
                    "Firebase App": !!app,
                    "Firebase Auth": !!auth,
                    "Firebase Firestore": !!db,
                    "Current User": auth.currentUser ? auth.currentUser.email : "Not logged in",
                    "Auth State": auth.currentUser ? "Authenticated" : "Not authenticated"
                };
                
                results.innerHTML = '<div class="status success">✅ Firebase connection test passed</div>';
                debug.textContent = JSON.stringify(info, null, 2);
            } catch (error) {
                results.innerHTML = '<div class="status error">❌ Firebase connection test failed: ' + error.message + '</div>';
                debug.textContent = error.stack;
            }
        };

        window.testAuthState = function() {
            const results = document.getElementById('testResults');
            const debug = document.getElementById('debugInfo');
            
            const user = auth.currentUser;
            if (user) {
                const info = {
                    "User UID": user.uid,
                    "User Email": user.email,
                    "Display Name": user.displayName,
                    "Email Verified": user.emailVerified,
                    "Is Admin": user.email === '<EMAIL>'
                };
                
                results.innerHTML = '<div class="status success">✅ User is authenticated</div>';
                debug.textContent = JSON.stringify(info, null, 2);
            } else {
                results.innerHTML = '<div class="status error">❌ No user is currently logged in</div>';
                debug.textContent = "Please log in first using the auth page";
            }
        };

        window.testFormFields = function() {
            const results = document.getElementById('testResults');
            const debug = document.getElementById('debugInfo');
            
            // Test if we can access the auth page form fields
            try {
                // This will only work if we're on the auth page
                const adminFields = {
                    "adminFullName": !!document.getElementById('adminFullName'),
                    "adminGender": !!document.getElementById('adminGender'),
                    "adminBirthdate": !!document.getElementById('adminBirthdate')
                };
                
                const studentFields = {
                    "studentFullName": !!document.getElementById('studentFullName'),
                    "studentGender": !!document.getElementById('studentGender'),
                    "studentBirthdate": !!document.getElementById('studentBirthdate'),
                    "studentId": !!document.getElementById('studentId'),
                    "school": !!document.getElementById('school'),
                    "schoolClass": !!document.getElementById('schoolClass'),
                    "parentName": !!document.getElementById('parentName'),
                    "parentPhone": !!document.getElementById('parentPhone'),
                    "courseClass": !!document.getElementById('courseClass')
                };
                
                const info = {
                    "Admin Fields": adminFields,
                    "Student Fields": studentFields,
                    "Note": "This test only works on the auth page"
                };
                
                results.innerHTML = '<div class="status info">ℹ️ Form field test completed (check debug info)</div>';
                debug.textContent = JSON.stringify(info, null, 2);
            } catch (error) {
                results.innerHTML = '<div class="status error">❌ Form field test failed: ' + error.message + '</div>';
                debug.textContent = error.stack;
            }
        };

        window.testFirebaseWrite = async function() {
            const results = document.getElementById('testResults');
            const debug = document.getElementById('debugInfo');
            
            const user = auth.currentUser;
            if (!user) {
                results.innerHTML = '<div class="status error">❌ No user logged in for write test</div>';
                debug.textContent = "Please log in first";
                return;
            }

            try {
                const testData = {
                    testField: "profile fix test",
                    timestamp: new Date().toISOString(),
                    testType: "write_permission_test"
                };
                
                await setDoc(doc(db, "users", user.uid), testData, { merge: true });
                
                // Try to read back
                const readDoc = await getDoc(doc(db, "users", user.uid));
                const readData = readDoc.exists() ? readDoc.data() : null;
                
                results.innerHTML = '<div class="status success">✅ Firebase write test successful</div>';
                debug.textContent = JSON.stringify({
                    "Written Data": testData,
                    "Read Back Data": readData,
                    "Test Result": "SUCCESS"
                }, null, 2);
            } catch (error) {
                results.innerHTML = '<div class="status error">❌ Firebase write test failed: ' + error.message + '</div>';
                debug.textContent = JSON.stringify({
                    "Error Code": error.code,
                    "Error Message": error.message,
                    "Error Stack": error.stack
                }, null, 2);
            }
        };

        window.openAuthPage = function() {
            window.open('./auth/', '_blank');
        };

        // Monitor auth state changes
        onAuthStateChanged(auth, (user) => {
            const statusDiv = document.querySelector('.status.info');
            if (user) {
                statusDiv.innerHTML = '<strong>Auth Status:</strong> ✅ Logged in as ' + user.email;
                statusDiv.className = 'status success';
            } else {
                statusDiv.innerHTML = '<strong>Auth Status:</strong> ❌ Not logged in';
                statusDiv.className = 'status error';
            }
        });
    </script>
</body>
</html>
